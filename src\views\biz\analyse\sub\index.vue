<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="areaName"
            placeholder="输入关键字以查找"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="meterTreeOptions"
            :props="props"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            show-checkbox
            default-expand-all
            highlight-current
          />
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
          <el-form-item label="点位类型" prop="pointType">
            <el-select
              v-model="queryParams.pointType"
              placeholder="请选择"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="option in dict.type.point_type"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期时间"
              end-placeholder="结束日期时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleDateRangeChange"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>


      </el-col>
    </el-row>
  </div>
</template>

<script>


import {subAnalyse} from "@/api/biz/subAnalyse";
import TimeAnalysisSelector from "@/components/TimeAnalysisSelector/index.vue";
import {meterTree} from "@/api/biz/meter";

export default {
  components: {TimeAnalysisSelector},
  dicts: ['point_type'],
  data() {
    return {
      // 时间范围选择器
      dateRange: [],

      meterTreeOptions: [],
      props: {
        multiple: true, emitPath: false,
        value: 'id', // 绑定的值字段名
        label: 'name', // 显示的文字字段名
        children: 'children' // 子选项字段名
      },
      // 区域名称
      areaName: undefined,

      // 查询参数
      queryParams: {
        meterIds: [],
        pointType: null,
        startTime: null,
        endTime: null
      },

    }
  },
  watch: {
    // 根据名称筛选部门树
    areaName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getMeterTree()
  },
  methods: {
    // 获取区域表具树结构
    getMeterTree() {
      this.typeLoading = true
      meterTree({
        energyType: this.energyType
      }).then(res => {
        this.meterTreeOptions = res.data
        this.typeLoading = false
      })
    },
    getSubAnalyseData() {
      this.loading = true
      subAnalyse(this.queryParams).then(data => {
        console.log(data)
        this.loading = false
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置时间选择器
      this.$refs.timeAnalysisSelector.reset()
      // 重置其他参数
      this.queryParams.meterIds = []
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10

      // 清除树选中状态 - 使用setCheckedKeys清空所有选中项
      this.$refs.tree.setCheckedKeys([])

      // 清空表格数据
      this.tableData = []
      this.total = 0
      this.dateColumns = {}
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (!this.verify()) {
        return
      }
      this.queryParams.pageNum = 1
      this.getSubAnalyseData()
    },
    verify() {
      // 检查必要参数
      if (!this.queryParams.startTime || !this.queryParams.endTime || !this.queryParams.pointType) {
        this.$message.warning('请选择表具、点位、时间范围');
        return false;
      }

      // 获取选中的节点
      const checkedNodes = this.$refs.tree.getCheckedNodes();

      // 筛选出类型为 "meter" 的节点 ID
      this.queryParams.meterIds = checkedNodes
        .filter(node => node.type === 'meter')
        .map(node => node.id);

      // 检查是否选择了表具
      if (this.queryParams.meterIds.length === 0) {
        this.$message.warning('请至少选择一个表具');
        return false;
      }
      return true;
    },
    // 时间范围选择器变化
    handleDateRangeChange(val) {
      if (val) {
        this.queryParams.startTime = val[0]
        this.queryParams.endTime = val[1]
      } else {
        this.queryParams.startTime = undefined
        this.queryParams.endTime = undefined
      }
    },
  }
}

</script>


<style>

</style>
